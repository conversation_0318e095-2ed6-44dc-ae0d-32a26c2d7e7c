import request from '@/config/axios'

export const DrawingApi = {
  getQueryPartKind: () => {
    return request.post({ url: `/informatization/development/drawings/queryPartKind` })
  },

  getQueryDocumentByItemCode: (params: any) => {
    return request.post({
      url: `/informatization/development/drawings/queryDocumentByItemCode`,
      data: params
    })
  },

  getPage: (params: any) => {
    return request.post({ url: `/informatization/development/drawings/page`, data: params })
  },

  getDocPathOrWx: (params: any) => {
    return request.post({
      url: `/informatization/development/drawings/getDocPathOrWx`,
      data: params
    })
  },
  destoryOrWx: (params: any) => {
    return request.post({ url: `/informatization/development/drawings/destoryOrWx`, data: params })
  },

  addDrawingLog: (params: any) => {
    return request.post({
      url: `/informatization/development/drawings/addDrawingLog`,
      data: params
    })
  },
  getDrawingLogPage: (params: any) => {
    return request.post({ url: `/informatization/development/drawings/pageLog`, data: params })
  }
}
