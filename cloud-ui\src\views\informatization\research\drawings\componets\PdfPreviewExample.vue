<template>
  <div class="pdf-example-container">
    <div class="header">
      <h1>PDF预览组件使用示例</h1>
      <p>优化后的PDF预览组件，提供高质量的文档显示效果</p>
    </div>

    <div class="demo-section">
      <div class="demo-controls">
        <el-input
          v-model="pdfUrl"
          placeholder="请输入PDF文件URL"
          style="width: 400px; margin-right: 10px;"
        />
        <el-button type="primary" @click="loadPdf" :disabled="!pdfUrl">
          加载PDF
        </el-button>
        <el-button @click="loadSamplePdf">
          加载示例PDF
        </el-button>
      </div>

      <div class="quality-settings">
        <h3>质量设置</h3>
        <el-row :gutter="20">
          <el-col :span="8">
            <label>初始缩放:</label>
            <el-slider
              v-model="initialZoom"
              :min="0.5"
              :max="2"
              :step="0.1"
              show-input
              @change="reloadPdf"
            />
          </el-col>
          <el-col :span="8">
            <label>渲染质量:</label>
            <el-select v-model="renderQuality" @change="reloadPdf">
              <el-option label="标准" value="standard" />
              <el-option label="高质量" value="high" />
              <el-option label="超高质量" value="ultra" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-checkbox v-model="enableSharpening" @change="reloadPdf">
              启用锐化滤镜
            </el-checkbox>
          </el-col>
        </el-row>
      </div>
    </div>

    <div class="pdf-viewer-section" v-if="currentPdfUrl">
      <PdfPreview
        :pdf-url="currentPdfUrl"
        :initial-zoom="initialZoom"
        :key="pdfKey"
      />
    </div>

    <div class="tips-section">
      <h3>使用提示</h3>
      <el-alert
        title="优化效果说明"
        type="info"
        :closable="false"
        show-icon
      >
        <ul>
          <li><strong>高分辨率渲染</strong>: 自动适配设备像素比，确保在高DPI显示器上清晰显示</li>
          <li><strong>智能缩放</strong>: 双重缩放策略，平衡渲染质量与性能</li>
          <li><strong>字体优化</strong>: 特别针对中文字体进行优化，提高可读性</li>
          <li><strong>颜色管理</strong>: 使用标准颜色空间，确保颜色准确性</li>
          <li><strong>后处理增强</strong>: 可选的锐化滤镜，进一步提升文字清晰度</li>
        </ul>
      </el-alert>

      <el-alert
        title="性能建议"
        type="warning"
        :closable="false"
        show-icon
        style="margin-top: 10px;"
      >
        <ul>
          <li>大文件（>10MB）可能需要较长加载时间</li>
          <li>超高质量模式会消耗更多内存和CPU资源</li>
          <li>在移动设备上建议使用标准质量模式</li>
          <li>确保PDF文件本身质量良好以获得最佳效果</li>
        </ul>
      </el-alert>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import PdfPreview from './PdfPreview.vue'

// 响应式数据
const pdfUrl = ref('')
const currentPdfUrl = ref('')
const initialZoom = ref(1.0)
const renderQuality = ref('high')
const enableSharpening = ref(true)
const pdfKey = ref(0)

// 加载PDF
const loadPdf = () => {
  if (pdfUrl.value.trim()) {
    currentPdfUrl.value = pdfUrl.value.trim()
    pdfKey.value++
  }
}

// 加载示例PDF
const loadSamplePdf = () => {
  // 这里可以设置一个示例PDF的URL
  pdfUrl.value = '/sample.pdf' // 替换为实际的示例PDF URL
  loadPdf()
}

// 重新加载PDF（当设置改变时）
const reloadPdf = () => {
  if (currentPdfUrl.value) {
    pdfKey.value++
  }
}
</script>

<style scoped>
.pdf-example-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f5f5f5;
}

.header {
  text-align: center;
  margin-bottom: 30px;
}

.header h1 {
  color: #333;
  margin-bottom: 10px;
}

.header p {
  color: #666;
  font-size: 16px;
}

.demo-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.demo-controls {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.quality-settings h3 {
  margin-bottom: 15px;
  color: #333;
}

.quality-settings label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #555;
}

.pdf-viewer-section {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.tips-section {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tips-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.tips-section ul {
  margin: 10px 0;
  padding-left: 0;
}

.tips-section li {
  list-style: none;
  margin: 8px 0;
  padding-left: 20px;
  position: relative;
}

.tips-section li::before {
  content: "•";
  color: #409eff;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-example-container {
    padding: 10px;
  }
  
  .demo-controls {
    flex-direction: column;
    gap: 10px;
  }
  
  .demo-controls .el-input {
    width: 100% !important;
  }
}
</style>
