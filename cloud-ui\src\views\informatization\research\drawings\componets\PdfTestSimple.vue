<template>
  <div class="pdf-test-simple">
    <h2>PDF预览测试</h2>
    <div class="test-info">
      <p>当前优化状态：基础高质量渲染（兼容模式）</p>
      <ul>
        <li>✅ 使用1.5-2.5倍像素比提高清晰度</li>
        <li>✅ 启用图像平滑和高质量设置</li>
        <li>✅ 优化Canvas渲染参数</li>
        <li>✅ 改进背景和对比度</li>
        <li>⚠️ 暂时禁用后处理以确保兼容性</li>
      </ul>
    </div>
    
    <div class="pdf-container">
      <PdfPreview 
        v-if="pdfUrl" 
        :pdf-url="pdfUrl" 
        :initial-zoom="1.0"
      />
      <div v-else class="no-pdf">
        <p>请在代码中设置PDF URL进行测试</p>
        <code>pdfUrl.value = 'your-pdf-url-here'</code>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import PdfPreview from './PdfPreview.vue'

const pdfUrl = ref('')

onMounted(() => {
  // 在这里设置你的测试PDF URL
  // pdfUrl.value = '/path/to/your/test.pdf'
  
  // 或者使用一个在线PDF进行测试
  // pdfUrl.value = 'https://example.com/sample.pdf'
})
</script>

<style scoped>
.pdf-test-simple {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f5f5f5;
}

.pdf-test-simple h2 {
  margin-bottom: 20px;
  color: #333;
}

.test-info {
  background: white;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-info p {
  margin-bottom: 10px;
  font-weight: 500;
  color: #333;
}

.test-info ul {
  margin: 0;
  padding-left: 20px;
}

.test-info li {
  margin: 5px 0;
  color: #666;
}

.pdf-container {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.no-pdf {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #666;
}

.no-pdf code {
  background: #f0f0f0;
  padding: 8px 12px;
  border-radius: 4px;
  margin-top: 10px;
  font-family: 'Courier New', monospace;
}
</style>
