import request from '@/config/axios'

export const InformationBiApi = {
  //获取信息部指标列表
  getInformationList: (params) => {
    return request.get({ url: `/informatization/hr/dept-indicator/list-information`, params })
  },
  //
  getFlowList: (params: any) => {
    return request.get({ url: '/informatization/ekp/information/flow/get-flow-list', params })
  },
  // 更新信息部指标
  updateInformation: (data) => {
    return request.post({ url: `/informatization/hr/dept-indicator/update-information`, data })
  },
  saveFlowPerson: (data: any) => {
    return request.post({
      url: '/informatization/report/ekp/information/flow/save-flow-person',
      data
    })
  }
}
