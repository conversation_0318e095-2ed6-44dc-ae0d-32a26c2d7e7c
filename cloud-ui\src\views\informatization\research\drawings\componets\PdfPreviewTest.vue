<template>
  <div class="pdf-test-container">
    <h2>PDF预览组件测试</h2>
    <div class="test-controls">
      <el-button @click="loadTestPdf">加载测试PDF</el-button>
      <el-button @click="toggleQualityMode">切换质量模式: {{ qualityMode }}</el-button>
    </div>
    
    <div class="pdf-preview-wrapper">
      <PdfPreview 
        v-if="pdfUrl" 
        :pdf-url="pdfUrl" 
        :initial-zoom="1.0"
        :key="pdfKey"
      />
    </div>
    
    <div class="quality-info">
      <h3>优化说明：</h3>
      <ul>
        <li>使用高分辨率渲染（2-4倍像素比）</li>
        <li>启用图像平滑和高质量设置</li>
        <li>优化字体渲染和颜色管理</li>
        <li>应用轻微锐化滤镜提高文字清晰度</li>
        <li>使用标准颜色空间确保颜色准确性</li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import PdfPreview from './PdfPreview.vue'

const pdfUrl = ref('')
const pdfKey = ref(0)
const qualityMode = ref('高质量')

const loadTestPdf = () => {
  // 这里可以设置你的测试PDF URL
  pdfUrl.value = '/test.pdf' // 替换为实际的PDF URL
  pdfKey.value++
}

const toggleQualityMode = () => {
  // 这个功能可以用来测试不同的质量设置
  qualityMode.value = qualityMode.value === '高质量' ? '标准质量' : '高质量'
  // 重新加载组件以应用新设置
  pdfKey.value++
}
</script>

<style scoped>
.pdf-test-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
}

.test-controls {
  margin-bottom: 20px;
  display: flex;
  gap: 10px;
}

.pdf-preview-wrapper {
  flex: 1;
  border: 1px solid #ddd;
  border-radius: 8px;
  overflow: hidden;
}

.quality-info {
  margin-top: 20px;
  padding: 15px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.quality-info h3 {
  margin-top: 0;
  color: #333;
}

.quality-info ul {
  margin: 10px 0;
  padding-left: 20px;
}

.quality-info li {
  margin: 5px 0;
  color: #666;
}
</style>
