import request from '@/config/axios'

export const E<PERSON><PERSON>baoDeptApi = {
  /** 获取合思部门列 */
  getTableColumn: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/dept/get-columns' })
  },
  /** 获取U9所有的部门 */
  getErpAllDept: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/dept/get-erp-all-dept' })
  },
  /** 获取合思部门分页 */
  page: async (params: any) => {
    return await request.get({ url: '/butt-joint/ekuaibao/dept/page', params })
  },
  /** 更新部门 */
  save: async (data: any) => {
    return await request.post({ url: '/butt-joint/ekuaibao/dept/save', data })
  }
}

export const EkuaibaoErpApi = {
  /** 获取ERP项目列 */
  getProrjectTableColumn: async () => {
    return await request.get({ url: '/butt-joint/erp/project/get-columns' })
  },
  /** 获取ERP项目分页 */
  getErpProjectPage: async (params: any) => {
    return await request.get({ url: '/butt-joint/erp/project/page', params })
  },

  getJstToken: async () => {
    return await request.get({ url: '/butt-joint/erp/project/get-jst-token' })
  },

  updateJstToken: async (token: string) => {
    return await request.post({ url: '/butt-joint/erp/project/update-jst-token?token=' + token })
  }
}

export const EkuaibaoCustomApi = {
  /** 推送本地数据到合思自定义档案 */
  sendData: async (dimensionId: string, code: string, name: string) => {
    return await request.post({
      url: `/butt-joint/ekuaibao/custom/send-data?dimensionId=${dimensionId}&code=${code}&name=${name}`
    })
  }
}

export const EkuaibaoOrderApi = {
  /** 获取合思单据列 */
  getTableColumn: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/get-columns' })
  },
  /** 获取合思单据分页数据 */
  page: async (params: any) => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/page', params })
  },
  /** 从合思获取单据数据 */
  updateOrder: async (orderId: string) => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/update-order/' + orderId })
  },
  /** 同步合思单据数据到ERP */
  syncOrder: async (orderId: string) => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/sync-order/' + orderId })
  },
  /** 不同步合思单据数据到ERP */
  unSyncOrder: async (orderId: string) => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/un-sync-order/' + orderId })
  },
  downloadOrder: async (code: string) => {
    return await request.get({ url: '/butt-joint/ekuaibao/order/download-order/' + code })
  }
}

export const EkuaibaoSubjectApi = {
  /** 获取合思科目列 */
  getTableColumn: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/subject/get-columns' })
  },
  /** 获取U9所有的科目信息 */
  getErpAllSubject: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/subject/get-erp-all-subject' })
  },
  /** 获取合思科目分页数据 */
  page: async (params: any) => {
    return await request.get({ url: '/butt-joint/ekuaibao/subject/page', params })
  },
  /** 更新科目 */
  save: async (data: any) => {
    return await request.post({ url: '/butt-joint/ekuaibao/subject/save', data })
  }
}

export const EkuaibaoUserApi = {
  /** 获取合思用户列 */
  getTableColumn: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/user/get-columns' })
  },
  /** 获取U9所有的业务员信息 */
  getErpAllSeller: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/user/get-erp-all-seller' })
  },
  /** 获取U9所有的员工记录信息 */
  getErpAllEmployee: async () => {
    return await request.get({ url: '/butt-joint/ekuaibao/user/get-erp-all-employee' })
  },
  /** 获取合思用户分页数据 */
  page: async (params: any) => {
    return await request.get({ url: '/butt-joint/ekuaibao/user/page', params })
  },
  /** 更新用户 */
  save: async (data: any) => {
    return await request.post({ url: '/butt-joint/ekuaibao/user/save', data })
  }
}
