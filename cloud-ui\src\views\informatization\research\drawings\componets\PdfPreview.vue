<template>
  <div class="pdf-viewer-container">
    <!-- 顶部状态栏 -->
    <div class="pdf-status">
      <span v-if="!loading && !error">
        {{ fileName }}
      </span>
    </div>

    <!-- PDF渲染区域 -->
    <div class="pdf-container" @wheel="handleWheel" ref="pdfContainer">
      <div
        class="pdf-wrapper"
        :class="{ dragging: isDragging }"
        :style="wrapperStyle"
        @mousedown="startDrag"
        @mousemove="onDrag"
        @mouseup="endDrag"
        @mouseleave="endDrag"
        ref="pdfWrapper"
      >
        <div v-if="loading" class="loading-indicator">
          <i class="fas fa-spinner fa-spin"></i>
          <span>加载中...</span>
        </div>

        <div v-else-if="error" class="error-message">
          <i class="fas fa-exclamation-triangle"></i>
          <span>{{ error }}</span>
        </div>

        <div v-else class="pdf-pages">
          <!-- 只渲染当前页 -->
          <div class="pdf-page-container">
            <canvas
              v-if="currentPageCanvas"
              :data-page-number="currentPage"
              class="pdf-page"
              :class="{ 'active-page': true, 'rendering': isRendering }"
            ></canvas>

            <!-- 渲染时的加载遮罩 -->
            <div v-if="isRendering" class="rendering-overlay">
              <div class="rendering-spinner">
                <i class="fas fa-sync-alt fa-spin"></i>
                <span>渲染中...</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部控制栏 -->
    <div class="pdf-controls">
      <div class="control-group">
        <button
          class="control-btn"
          @click="prevPage"
          :disabled="currentPage <= 1"
          aria-label="上一页"
        >
          <Icon icon="ep:arrow-left-bold" />
        </button>
        <span class="page-info">
          第
          <el-input
            type="number"
            v-model="inputPage"
            placeholder="页码"
            :min="1"
            style="width: 60px"
            :max="totalPages"
            @keydown.enter="goToInputPage"
            @blur="goToInputPage"
          />
          / {{ totalPages }} 页
        </span>
        <button
          class="control-btn"
          @click="nextPage"
          :disabled="currentPage >= totalPages"
          aria-label="下一页"
        >
          <Icon icon="ep:arrow-right-bold" />
        </button>
      </div>

      <div class="control-group zoom-controls">
        <button class="control-btn" @click="zoomOut" :disabled="zoom <= 0.2" aria-label="缩小">
          <Icon icon="ep:minus" />
        </button>
        <span class="zoom-level">{{ Math.round(zoom * 100) }}%</span>
        <button class="control-btn" @click="zoomIn" :disabled="zoom >= 2" aria-label="放大">
          <Icon icon="ep:plus" />
        </button>
        <button class="control-btn" @click="rotateLeft" aria-label="向左旋转">
          <Icon icon="ep:refresh-left" />
        </button>
        <button class="control-btn" @click="rotateRight" aria-label="向右旋转">
          <Icon icon="ep:refresh-right" />
        </button>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick, computed } from 'vue'
import * as pdfjsLib from 'pdfjs-dist/build/pdf'

// 设置 PDF.js worker - 确保版本匹配
pdfjsLib.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js'

// 调试信息：打印版本信息
console.log('PDF.js 版本信息:', {
  version: pdfjsLib.version || 'unknown',
  workerSrc: pdfjsLib.GlobalWorkerOptions.workerSrc
})

// 在状态定义中添加旋转角度
const rotation = ref(0) // 旋转角度

// 添加旋转功能函数
const rotateLeft = () => {
  rotation.value = (rotation.value - 90) % 360
}

const rotateRight = () => {
  rotation.value = (rotation.value + 90) % 360
}

// Props
const props = defineProps({
  // PDF文件URL
  pdfUrl: {
    type: String,
    required: true
  },
  // 初始页码
  initialPage: {
    type: Number,
    default: 1
  },
  // 初始缩放比例
  initialZoom: {
    type: Number,
    default: 1
  }
})

// Refs
const pdfWrapper = ref<any>()
const pdfContainer = ref<any>()

// State
const pdfDoc = ref<any>()
const totalPages = ref(0)
const currentPage = ref(props.initialPage)
const zoom = ref(props.initialZoom)
const loading = ref(true)
const error = ref('')
const fileName = ref('')
const currentPageCanvas = ref(true) // 控制当前页canvas的渲染
const isRendering = ref(false) // 控制渲染状态，防止黑影闪烁

// 添加输入页码的状态
const inputPage = ref(currentPage.value)
// 监听 currentPage 变化，同步更新 inputPage
watch(currentPage, (newPage) => {
  inputPage.value = newPage
})

// 跳转到输入的页码
const goToInputPage = () => {
  const page = parseInt(inputPage.value.toString(), 10)
  if (!isNaN(page) && page >= 1 && page <= totalPages.value) {
    currentPage.value = page
  } else {
    // 如果输入无效，恢复为当前页码
    inputPage.value = currentPage.value
  }
}
// 拖动相关状态
const isDragging = ref(false)
const dragStart = ref({ x: 0, y: 0 })
const offset = ref({ x: 0, y: 0 })
const startOffset = ref({ x: 0, y: 0 })

// 渲染任务管理
const renderingTasks = new Map()
let zoomTimeout = null

// 修改wrapperStyle计算属性
const wrapperStyle = computed(() => {
  return {
    transform: `scale(${zoom.value}) translate(${offset.value.x}px, ${offset.value.y}px) rotate(${rotation.value}deg)`,
    transformOrigin: 'center center',
    willChange: 'transform' // 提示浏览器该元素将要改变，优化性能
  }
})

// 提取文件名
const extractFileName = (url) => {
  try {
    const urlObj = new URL(url)
    const pathname = urlObj.pathname
    return pathname.split('/').pop() || '未知文件'
  } catch (e) {
    return url.split('/').pop() || '未知文件'
  }
}

// 加载PDF文档
const loadPdf = async () => {
  try {
    loading.value = true
    error.value = ''
    fileName.value = extractFileName(props.pdfUrl)

    console.log('props.pdfUrl:', props.pdfUrl)

    // 先获取PDF数据，然后加载
    const response = await fetch(props.pdfUrl)
    const arrayBuffer = await response.arrayBuffer()

    // 加载PDF，使用兼容的优化配置
    const loadingTask = pdfjsLib.getDocument({
      data: arrayBuffer,
      cMapUrl: '/cmaps/',
      cMapPacked: true,
      // 基础配置，确保兼容性
      verbosity: 0,
      disableAutoFetch: false,
      disableStream: false,
      disableRange: false,
      // 保守的优化选项
      maxImageSize: -1, // 不限制图像大小以保持质量
      disableColorSpace: false // 启用颜色管理
    })
    console.log('loadingTask:', loadingTask.promise)
    pdfDoc.value = await loadingTask.promise
    totalPages.value = pdfDoc.value.numPages

    // 确保初始页在有效范围内
    if (currentPage.value < 1 || currentPage.value > totalPages.value) {
      currentPage.value = 1
    }

    // 先设置 loading 为 false，让 DOM 更新
    loading.value = false

    // 等待 DOM 更新完成
    await nextTick()

    // 渲染初始页
    await renderPage(currentPage.value)
  } catch (err: any) {
    console.error('加载PDF失败:', err)
    error.value = `加载PDF失败: ${err.message || err}`
    loading.value = false
  }
}

// 渲染指定页码
const renderPage = async (pageNum) => {
  if (!pdfDoc.value || pageNum < 1 || pageNum > totalPages.value) {
    return
  }

  try {
    // 设置渲染状态，显示加载遮罩
    isRendering.value = true

    // 获取页面
    const page = await pdfDoc.value.getPage(pageNum)

    // 获取设备像素比，使用适中的倍数以平衡质量和兼容性
    const devicePixelRatio = window.devicePixelRatio || 1
    const pixelRatio = Math.min(Math.max(devicePixelRatio, 1.5), 2.5) // 限制在1.5-2.5倍之间

    // 获取未缩放的视图
    const unscaledViewport = page.getViewport({ scale: 1, rotation: rotation.value })

    // 获取容器实际可用尺寸
    const containerWidth = (pdfContainer.value?.clientWidth || window.innerWidth) - 40
    const containerHeight = (pdfContainer.value?.clientHeight || window.innerHeight - 120) - 40

    // 计算基础适应缩放比例
    const widthScale = containerWidth / unscaledViewport.width
    const heightScale = containerHeight / unscaledViewport.height
    const baseScale = Math.min(widthScale, heightScale) * 0.9

    // 计算最终缩放比例
    const finalScale = baseScale * zoom.value * pixelRatio

    // 设置视图
    const viewport = page.getViewport({
      scale: finalScale,
      rotation: rotation.value
    })

    // 等待 Vue 创建 DOM 元素
    currentPageCanvas.value = true
    await nextTick()

    // 获取 Vue 创建的 canvas 元素
    const canvas = document.querySelector(`canvas[data-page-number="${pageNum}"]`) as any
    if (!canvas) {
      console.error(`找不到页面 ${pageNum} 的 canvas 元素`)
      return
    }

    // 创建离屏Canvas进行双缓冲渲染，避免闪烁
    const offscreenCanvas = document.createElement('canvas')
    const offscreenContext = offscreenCanvas.getContext('2d', {
      // 基础高质量渲染选项，确保兼容性
      alpha: false, // 禁用透明度以提高性能和质量
      willReadFrequently: false // 优化写入性能
    })

    const context = canvas.getContext('2d', {
      alpha: false,
      willReadFrequently: false
    })

    // 设置离屏Canvas尺寸（用于实际渲染）
    offscreenCanvas.width = viewport.width
    offscreenCanvas.height = viewport.height

    // 设置显示Canvas尺寸
    canvas.width = viewport.width
    canvas.height = viewport.height

    // 设置canvas显示尺寸（缩小显示以提高清晰度）
    const displayWidth = viewport.width / pixelRatio
    const displayHeight = viewport.height / pixelRatio
    canvas.style.width = `${Math.round(displayWidth)}px`
    canvas.style.height = `${Math.round(displayHeight)}px`

    // 重置变换矩阵
    offscreenContext.setTransform(1, 0, 0, 1, 0, 0)
    context.setTransform(1, 0, 0, 1, 0, 0)

    // 设置离屏Canvas的高质量渲染参数
    if (offscreenContext) {
      offscreenContext.imageSmoothingEnabled = true // 启用图像平滑
      if (offscreenContext.imageSmoothingQuality) {
        offscreenContext.imageSmoothingQuality = 'high' // 设置为最高质量（如果支持）
      }

      // 设置文本渲染优化
      if (offscreenContext.textRenderingOptimization) {
        offscreenContext.textRenderingOptimization = 'optimizeQuality'
      }
    }

    // 渲染页面到离屏Canvas，使用兼容的高质量设置
    const renderContext = {
      canvasContext: offscreenContext,
      viewport: viewport,
      enableWebGL: false,
      renderInteractiveForms: false,
      intent: 'display',
      annotationMode: 0,
      optionalContentConfigPromise: null
    }

    // 取消之前的渲染任务
    if (renderingTasks.has(pageNum)) {
      const previousTask = renderingTasks.get(pageNum)
      if (previousTask && previousTask.cancel) {
        previousTask.cancel()
      }
    }

    const renderTask = page.render(renderContext)
    renderingTasks.set(pageNum, renderTask)

    await renderTask.promise
    renderingTasks.delete(pageNum)

    // 渲染完成后，将离屏Canvas内容一次性复制到显示Canvas，避免闪烁
    if (offscreenContext && context) {
      context.clearRect(0, 0, canvas.width, canvas.height)
      context.drawImage(offscreenCanvas, 0, 0)
    }

    // 暂时禁用后处理以确保兼容性
    // postProcessCanvas(context, canvas)

    // 渲染完成，关闭加载状态
    isRendering.value = false

    // 滚动到当前页
    scrollToCurrentPage()
  } catch (err: any) {
    // 渲染失败也要关闭加载状态
    isRendering.value = false

    if (err.name !== 'RenderingCancelledException') {
      console.error(`渲染第${pageNum}页失败:`, err)
      error.value = `渲染第${pageNum}页失败`
    }
  }
}

// 后处理Canvas以提高显示质量
const postProcessCanvas = (context, canvas) => {
  try {
    // 应用锐化滤镜以提高文字清晰度
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height)
    const data = imageData.data

    // 简单的锐化算法
    const sharpenKernel = [
      0, -1, 0,
      -1, 5, -1,
      0, -1, 0
    ]

    // 应用轻微的锐化效果（仅对文字区域）
    applySharpenFilter(data, canvas.width, canvas.height, sharpenKernel, 0.3)

    context.putImageData(imageData, 0, 0)
  } catch (error) {
    // 如果后处理失败，不影响主要渲染
    console.warn('Canvas后处理失败:', error)
  }
}

// 应用锐化滤镜
const applySharpenFilter = (data, width, height, kernel, intensity) => {
  const output = new Uint8ClampedArray(data)

  for (let y = 1; y < height - 1; y++) {
    for (let x = 1; x < width - 1; x++) {
      for (let c = 0; c < 3; c++) { // RGB通道
        let sum = 0
        for (let ky = -1; ky <= 1; ky++) {
          for (let kx = -1; kx <= 1; kx++) {
            const idx = ((y + ky) * width + (x + kx)) * 4 + c
            sum += data[idx] * kernel[(ky + 1) * 3 + (kx + 1)]
          }
        }

        const idx = (y * width + x) * 4 + c
        const original = data[idx]
        const sharpened = Math.max(0, Math.min(255, sum))
        output[idx] = original + (sharpened - original) * intensity
      }
    }
  }

  // 复制处理后的数据
  for (let i = 0; i < data.length; i++) {
    data[i] = output[i]
  }
}

// 滚动到当前页
const scrollToCurrentPage = () => {
  nextTick(() => {
    const activePage = pdfWrapper.value.querySelector(
      `canvas[data-page-number="${currentPage.value}"]`
    )
    if (activePage) {
      const container = pdfContainer.value
      const scrollPos = activePage.offsetTop - container.offsetHeight / 4
      container.scrollTo({ top: scrollPos, behavior: 'smooth' })
    }
  })
}

// 翻页功能
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--
  }
}

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++
  }
}

const goToPage = (pageNum) => {
  if (pageNum >= 1 && pageNum <= totalPages.value) {
    currentPage.value = pageNum
  }
}

// 缩放功能 - 添加防抖动优化
let zoomDebounceTimer: any | null = null

const zoomIn = () => {
  if (zoom.value < 3) {
    zoom.value = Math.min(3, zoom.value + 0.1)

    // 立即显示加载状态
    isRendering.value = true

    // 防抖动优化渲染
    if (zoomDebounceTimer) {
      clearTimeout(zoomDebounceTimer)
    }
    zoomDebounceTimer = setTimeout(() => {
      renderPage(currentPage.value)
    }, 100)
  }
}

const zoomOut = () => {
  if (zoom.value > 0.1) {
    zoom.value = Math.max(0.1, zoom.value - 0.1)

    // 立即显示加载状态
    isRendering.value = true

    // 防抖动优化渲染
    if (zoomDebounceTimer) {
      clearTimeout(zoomDebounceTimer)
    }
    zoomDebounceTimer = setTimeout(() => {
      renderPage(currentPage.value)
    }, 100)
  }
}

// 鼠标滚轮缩放功能
// 鼠标滚轮缩放功能 - 优化动画流畅度
const handleWheel = (event) => {
  event.preventDefault()

  // 获取鼠标在容器中的位置
  const containerRect = pdfContainer.value.getBoundingClientRect()
  const mouseX = event.clientX - containerRect.left
  const mouseY = event.clientY - containerRect.top

  // 计算当前中心点相对于内容的位置
  const containerCenterX = containerRect.width / 2
  const containerCenterY = containerRect.height / 2

  // 计算偏移量
  const offsetX = (mouseX - containerCenterX) / zoom.value
  const offsetY = (mouseY - containerCenterY) / zoom.value

  // 根据滚轮方向调整缩放级别
  const zoomFactor = 0.05
  let zoomChanged = false

  if (event.deltaY < 0) {
    if (zoom.value < 3) {
      zoom.value = Math.min(3, zoom.value + zoomFactor)
      zoomChanged = true
    }
  } else {
    if (zoom.value > 0.1) {
      zoom.value = Math.max(0.1, zoom.value - zoomFactor)
      zoomChanged = true
    }
  }

  // 只有在缩放发生变化时才显示加载状态
  if (zoomChanged) {
    // 立即显示加载状态
    isRendering.value = true

    // 调整偏移量以保持鼠标位置相对不变
    offset.value.x = offset.value.x - offsetX * zoomFactor * (event.deltaY < 0 ? 1 : -1)
    offset.value.y = offset.value.y - offsetY * zoomFactor * (event.deltaY < 0 ? 1 : -1)

    // 防抖动优化渲染
    if (zoomDebounceTimer) {
      clearTimeout(zoomDebounceTimer)
    }
    zoomDebounceTimer = setTimeout(() => {
      renderPage(currentPage.value)
    }, 150)
  }
}

// 拖动功能
// 拖动功能
const startDrag = (event) => {
  // 只响应鼠标左键
  if (event.button !== 0) return

  isDragging.value = true
  dragStart.value = { x: event.clientX, y: event.clientY }
  startOffset.value = { ...offset.value }

  // 防止文本选择
  event.preventDefault()
}

// 拖动功能 - 使用 requestAnimationFrame 优化
let dragRAF: number | null = null

const onDrag = (event) => {
  if (!isDragging.value) return

  // 取消之前的动画帧
  if (dragRAF) {
    cancelAnimationFrame(dragRAF)
  }

  // 使用 requestAnimationFrame 提升流畅度
  dragRAF = requestAnimationFrame(() => {
    const dx = event.clientX - dragStart.value.x
    const dy = event.clientY - dragStart.value.y

    offset.value.x = startOffset.value.x + dx / zoom.value
    offset.value.y = startOffset.value.y + dy / zoom.value

    dragRAF = null
  })
}

const endDrag = () => {
  isDragging.value = false

  if (dragRAF) {
    cancelAnimationFrame(dragRAF)
    dragRAF = null
  }
}

// 监听页码变化，重新渲染
watch(currentPage, async (newPage, oldPage) => {
  // 渲染当前页
  await renderPage(newPage)
})

// 监听缩放变化，重新渲染当前页面
watch(zoom, async () => {
  if (!pdfDoc.value) return

  // 取消所有正在进行的渲染任务
  renderingTasks.forEach((task) => {
    if (task && task.cancel) {
      task.cancel()
    }
  })
  renderingTasks.clear()

  // 重新渲染当前页
  await renderPage(currentPage.value)
})

// 修改监听PDF URL变化的逻辑，重置页码
watch(
  () => props.pdfUrl,
  async (newUrl, oldUrl) => {
    if (newUrl !== oldUrl) {
      // 重置页码为第一页
      currentPage.value = 1
      // 重置缩放和旋转
      zoom.value = props.initialZoom
      rotation.value = 0
      offset.value = { x: 0, y: 0 }
      // 重新加载PDF
      await loadPdf()
    }
  }
)

// 初始化
onMounted(() => {
  loadPdf()
})

// 清理函数
onBeforeUnmount(() => {
  // 清除缩放定时器
  if (zoomTimeout) {
    clearTimeout(zoomTimeout)
  }

  // 取消所有渲染任务
  renderingTasks.forEach((task) => {
    if (task && task.cancel) {
      task.cancel()
    }
  })
  renderingTasks.clear()

  // 销毁PDF文档
  if (pdfDoc.value) {
    pdfDoc.value.destroy()
  }
})
</script>

<style scoped>
.pdf-viewer-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: 100vh;
  background-color: #f5f5f7;
  color: #333;
}

.pdf-status {
  padding: 8px 20px;
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  font-size: 12px;
  color: #888;
}

.pdf-container {
  flex: 1;
  overflow: hidden;
  padding: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f8f9fa; /* 更柔和的背景色，提供更好的对比度 */
  position: relative;
  /* 添加纹理以减少眼疲劳 */
  background-image:
    radial-gradient(circle at 1px 1px, rgba(0,0,0,0.02) 1px, transparent 0);
  background-size: 20px 20px;
}

.pdf-wrapper {
  transition: transform 0.1s ease;
  transform-origin: center center;
  cursor: grab;
  display: flex;
  flex-direction: column;
  align-items: center;
  will-change: transform; /* 优化动画性能，间接提升清晰度 */
  /* 添加硬件加速 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 在现有样式基础上添加 */
.pdf-wrapper.dragging {
  cursor: grabbing;
  transition: none; /* 拖拽时禁用过渡动画 */
}

.pdf-wrapper:not(.dragging) {
  transition: transform 0.1s ease;
}

.pdf-wrapper:active {
  cursor: grabbing;
}

.pdf-pages {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
}

.pdf-page-container {
  position: relative;
  display: inline-block;
}

.pdf-page {
  border: 1px solid #ccc;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  background-color: white;
  transition: all 0.2s ease;
  cursor: pointer;

  /* 优化图像渲染 - 使用高质量设置 */
  image-rendering: -webkit-optimize-contrast;
  image-rendering: high-quality;
  image-rendering: -moz-crisp-edges;
  image-rendering: -o-crisp-edges;

  /* 硬件加速优化 */
  transform: translateZ(0);
  backface-visibility: hidden;
  will-change: transform;

  /* 字体渲染优化 */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  font-smooth: always;
  text-rendering: optimizeLegibility;

  /* 确保像素完美对齐 */
  transform-style: preserve-3d;

  /* 优化渲染性能和质量 */
  contain: layout style paint;

  /* 防止图像模糊 */
  -ms-interpolation-mode: bicubic;

  /* 确保清晰的边缘 */
  shape-rendering: crispEdges;

  /* 优化Canvas渲染 */
  image-orientation: from-image;
}

.pdf-page.active-page {
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  border-color: #b0b0b0;
}

.pdf-page.rendering {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

/* 渲染遮罩样式 */
.rendering-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  backdrop-filter: blur(2px);
  border-radius: 4px;
}

.rendering-spinner {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
}

.rendering-spinner i {
  font-size: 24px;
  color: #409eff;
}

.rendering-spinner span {
  font-weight: 500;
  letter-spacing: 0.5px;
}

/* 加载动画效果 */
@keyframes fadeInOut {
  0% { opacity: 0; transform: scale(0.9); }
  100% { opacity: 1; transform: scale(1); }
}

.rendering-overlay {
  animation: fadeInOut 0.2s ease-out;
}

/* 优化Canvas渲染时的过渡效果 */
.pdf-page {
  transition: opacity 0.2s ease, filter 0.2s ease;
}

.pdf-page.rendering {
  filter: brightness(0.95) contrast(0.95);
}

.loading-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #666;
  gap: 12px;
}

.loading-indicator i {
  font-size: 32px;
  color: #888;
}

.error-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #dc3545;
  gap: 8px;
  padding: 0 20px;
  text-align: center;
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background-color: #fff;
  border-top: 1px solid #e0e0e0;
  box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.05);
  z-index: 10;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 12px;
}

.control-btn {
  background-color: #f0f0f4;
  border: none;
  border-radius: 4px;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #333;
}

.control-btn:hover:not(:disabled) {
  background-color: #e0e0e4;
  transform: translateY(-1px);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info,
.zoom-level {
  font-size: 14px;
  color: #666;
  min-width: 80px;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-controls {
    flex-direction: column;
    gap: 10px;
    padding: 10px;
  }

  .pdf-container {
    padding: 10px;
  }
}
</style>
