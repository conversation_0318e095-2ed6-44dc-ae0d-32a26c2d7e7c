import request from '@/config/axios'

export const OrderBomTaskApi = {
  /** 获取客户信息 */
  getCustomerList: async (params: any) => {
    return await request.get({ url: '/butt-joint/erp/project/get-customer', params })
  },
  /** 获取料品信息 */
  getItemList: async (itemCode: string) => {
    return await request.get({ url: '/butt-joint/erp/project/get-item', params: { itemCode } })
  },
  /** 批量保存任务 */
  batchSaveTask: async (data: any[]) => {
    return await request.post({ url: '/informatization/eng/bom-task/batch-save', data })
  },
  /** 分页查询订单BOM事务 */
  pageTask: async (data: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/page', data })
  },
  getTask: (id: number) => {
    return request.get({ url: '/informatization/eng/bom-task/get/' + id })
  },
  /** 分页查询订单 BOM 事务详情 */
  pageDetail: async (data: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/page-detail', data })
  },
  /** 保存任务详情 */
  saveDetail: async (data: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/save-detail', data })
  },
  /** 查询修改日志 */
  pageLog: async (params: any) => {
    return await request.get({ url: '/informatization/eng/bom-task/page-log', params })
  },
  /** 导出事务 */
  exportDetail: (data: any) => {
    return request.downloadPost({ url: '/informatization/eng/bom-task/export-detail', data })
  },
  /** 导出事务 */
  export: (data: any) => {
    return request.downloadPost({ url: '/informatization/eng/bom-task/export', data })
  },

  /**
   * 获取月度准时交付率统计
   * @param params 查询参数 { month: string, year: string }
   */
  getDeliveryRate: async (params: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/monthly-rate', data: params })
  },

  /**
   * 获取年度趋势数据
   * @param params 查询参数 { year: string }
   */
  getTrendData: async (params: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/trend-data', data: params })
  },

  /**
   * 导出月度准时交付率统计
   * @param params 查询参数 { year: string }
   */
  exportDay: async (params: any) => {
    return await request.downloadPost({
      url: '/informatization/eng/bom-task/export-excel',
      data: params
    })
  },

  /**
   * 删除需求
   * @param params ids
   */
  batchDelete: async (ids: any) => {
    return await request.post({ url: '/informatization/eng/bom-task/batch-delete', data: ids })
  }
}
