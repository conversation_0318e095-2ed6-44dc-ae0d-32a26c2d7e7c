import request from '@/config/axios'

export const TechnologyCustReportApi = {
  // 获取报工信息分页数据
  getCustDeclarationPage: async (data: any) => {
    return await request.post({ url: '/informatization/cust-declaration/page', data })
  },

  // 更新报工信息
  update: async (data: any[]) => {
    return await request.post({ url: '/informatization/cust-declaration/update', data })
  },
  // 删除报工信息
  delete: async (id: number) => {
    return await request.get({ url: '/informatization/cust-declaration/delete/' + id })
  }
}

export const DefineApi = {
  // 保存或修改工艺定义
  saveOrUpdate: async (data: any) => {
    return await request.post({ url: '/informatization/technology-define/save-or-update', data })
  },
  // 获取工艺列表
  listDefine: async () => {
    return await request.get({ url: '/informatization/technology-define/list' })
  },
  // 删除工艺定义
  deleteDefine: async (id: number) => {
    return await request.get({ url: '/informatization/technology-define/delete/' + id })
  }
}

export const RouteApi = {
  // 根据id 获取工艺路线
  getRouteById: async (id: number) => {
    return await request.get({ url: '/informatization/technology-route/get/' + id })
  },
  // 保存工艺路线
  saveRoute: async (data: any) => {
    return await request.post({ url: '/informatization/technology-route/save', data })
  },
  // 获取工艺路线平铺分页
  getRoutePage: async (data: any) => {
    return await request.post({ url: '/informatization/technology-route/page', data })
  },
  // 获取工艺路线与物料关联分页
  getRouteLinkPage: async (data: any) => {
    return await request.post({ url: '/informatization/technology-route/page-link', data })
  },
  // 获取所有工艺路线的最新版本
  getRouteAllVersionList: async () => {
    return await request.get({ url: '/informatization/technology-route/get-route-all-version' })
  },
  // 保存或更新工艺路线与物料关联
  saveLink: async (data: any[]) => {
    return await request.post({ url: '/informatization/technology-route/save-link', data })
  },
  //** 获取条件设定下，匹配到的结果总数 */
  selectLinkCount: async (data: any) => {
    return await request.post({ url: '/informatization/technology-route/select-link-count', data })
  },
  /** 导出符合条件物料列表 */
  exportLink: async (data: any) => {
    return await request.downloadPost({
      url: '/informatization/technology-route/export-link',
      data
    })
  },
  /** 保存或更新工艺路线与物料关联 */
  saveLinkConditions: async (data: any) => {
    return await request.post({
      url: '/informatization/technology-route/save-link-conditions',
      data
    })
  },
  /** 导出工艺路线模板 */
  exportTemplate: async () => {
    return await request.downloadPost({ url: '/informatization/technology-route/export-template' })
  },
  exportLinkAll: async (data: any) => {
    return await request.downloadPost({
      url: '/informatization/technology-route/export-link-all',
      data
    })
  }
}
