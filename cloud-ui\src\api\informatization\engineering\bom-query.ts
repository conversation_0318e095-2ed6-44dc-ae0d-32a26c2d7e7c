import request from '@/config/axios'

export const BomQueryApi = {
  getForwardTableColumn: async () => {
    return await request.get({ url: '/informatization/bom/recursive/get-forward-table-column' })
  },
  getSoTableColumn: async () => {
    return await request.get({ url: '/informatization/bom/recursive/get-so-table-column' })
  },
  getReverseTableColumn: async () => {
    return await request.get({ url: '/informatization/bom/recursive/get-reverse-table-column' })
  },
  getItemTableColumn: async () => {
    return await request.get({ url: '/informatization/bom/recursive/get-item-table-column' })
  },
  getUnits: async () => {
    return await request.get({ url: '/informatization/bom/recursive/list-units' })
  },
  getOperators: async (type: number) => {
    return await request.get({ url: '/informatization/bom/recursive/list-operators/' + type })
  },
  getCustomer: async () => {
    return await request.get({ url: '/informatization/bom/recursive/list-customer' })
  },
  getForwardRecursivePage: async (params: any) => {
    return await request.get({ url: '/informatization/bom/recursive/page-forward', params })
  },
  getReverseRecursivePage: async (params: any) => {
    return await request.get({ url: '/informatization/bom/recursive/page-reverse', params })
  },
  getSoRecursivePage: async (params: any) => {
    return await request.get({ url: '/informatization/bom/recursive/page-so', params })
  },
  getItemRecursivePage: async (params: any) => {
    return await request.get({ url: '/informatization/bom/recursive/page-material', params })
  }
}
