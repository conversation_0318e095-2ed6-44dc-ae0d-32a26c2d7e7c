import request from '@/config/axios'

export const MaterialAttributeApi = {
  /** 获取料品分类 */
  getCategoryList: () => {
    return request.get({ url: '/butt-joint/plm/attribute/category' })
  },
  /** 获取分类属性 */
  getAttributeList: (id: string) => {
    return request.get({ url: '/butt-joint/plm/attribute/' + id })
  },
  /** 获取属性字典 */
  getAttributeDictList: (data: any) => {
    return request.post({ url: '/butt-joint/plm/attribute/dict', data })
  },
  /** 获取物料列表 */
  getItemList: (data: any) => {
    return request.post({ url: '/butt-joint/plm/attribute/item-list', data })
  }
}
