import request from '@/config/axios'

export const BomComparisonApi = {
  /** 创建推送规则 */
  createBomComparisonRule: (data: any) => {
    return request.post({ url: '/butt-joint/backend/bom-comparison/create-rule', data })
  },
  /** 获取推送规则 */
  getBomComparisonRule: () => {
    return request.post({ url: '/butt-joint/backend/bom-comparison/get-rule' })
  },
  /** 获取推送记录 */
  getBomComparisonList: (date: string) => {
    return request.post({ url: '/butt-joint/backend/bom-comparison/list', params: { date } })
  }
}
