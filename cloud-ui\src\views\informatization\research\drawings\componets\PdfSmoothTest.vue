<template>
  <div class="pdf-smooth-test">
    <div class="test-header">
      <h2>PDF缩放优化测试</h2>
      <div class="test-description">
        <p>✅ 已解决缩放时黑影闪烁问题</p>
        <ul>
          <li>🎯 双缓冲渲染技术，避免Canvas清空闪烁</li>
          <li>🔄 智能加载遮罩，提供视觉反馈</li>
          <li>⚡ 防抖动优化，减少不必要的重渲染</li>
          <li>🎨 平滑过渡动画，提升用户体验</li>
        </ul>
      </div>
    </div>

    <div class="test-controls">
      <div class="control-group">
        <label>测试PDF URL:</label>
        <el-input
          v-model="pdfUrl"
          placeholder="请输入PDF文件URL"
          style="width: 400px; margin-right: 10px;"
        />
        <el-button type="primary" @click="loadPdf" :disabled="!pdfUrl">
          加载PDF
        </el-button>
      </div>
      
      <div class="test-tips">
        <el-alert
          title="测试说明"
          type="info"
          :closable="false"
          show-icon
        >
          <p>请尝试以下操作来测试优化效果：</p>
          <ul>
            <li><strong>鼠标滚轮缩放</strong>：在PDF上滚动鼠标滚轮</li>
            <li><strong>按钮缩放</strong>：点击底部的放大/缩小按钮</li>
            <li><strong>观察效果</strong>：应该看到平滑的加载遮罩，而不是黑影闪烁</li>
          </ul>
        </el-alert>
      </div>
    </div>

    <div class="pdf-viewer-area" v-if="currentPdfUrl">
      <PdfPreview
        :pdf-url="currentPdfUrl"
        :initial-zoom="1.0"
        :key="pdfKey"
      />
    </div>

    <div v-else class="no-pdf-placeholder">
      <div class="placeholder-content">
        <i class="fas fa-file-pdf fa-3x"></i>
        <h3>请加载PDF文件进行测试</h3>
        <p>在上方输入PDF URL并点击加载按钮</p>
      </div>
    </div>

    <div class="optimization-details">
      <h3>优化技术详情</h3>
      <div class="tech-cards">
        <div class="tech-card">
          <h4>🖼️ 双缓冲渲染</h4>
          <p>使用离屏Canvas进行渲染，完成后一次性复制到显示Canvas，避免渲染过程中的黑影闪烁。</p>
        </div>
        
        <div class="tech-card">
          <h4>⏳ 智能加载状态</h4>
          <p>在缩放操作时显示美观的加载遮罩，提供清晰的视觉反馈，替代原来的黑影效果。</p>
        </div>
        
        <div class="tech-card">
          <h4>🚀 防抖动优化</h4>
          <p>对连续的缩放操作进行防抖处理，减少不必要的重渲染，提升性能和用户体验。</p>
        </div>
        
        <div class="tech-card">
          <h4>🎨 平滑动画</h4>
          <p>添加CSS过渡动画和滤镜效果，让缩放过程更加平滑自然。</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import PdfPreview from './PdfPreview.vue'

const pdfUrl = ref('')
const currentPdfUrl = ref('')
const pdfKey = ref(0)

const loadPdf = () => {
  if (pdfUrl.value.trim()) {
    currentPdfUrl.value = pdfUrl.value.trim()
    pdfKey.value++
  }
}
</script>

<style scoped>
.pdf-smooth-test {
  height: 100vh;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: #f5f5f5;
}

.test-header {
  margin-bottom: 20px;
}

.test-header h2 {
  color: #333;
  margin-bottom: 10px;
}

.test-description {
  background: white;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-description p {
  margin-bottom: 10px;
  font-weight: 500;
  color: #27ae60;
}

.test-description ul {
  margin: 0;
  padding-left: 20px;
}

.test-description li {
  margin: 5px 0;
  color: #666;
}

.test-controls {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.control-group {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.control-group label {
  margin-right: 10px;
  font-weight: 500;
  color: #333;
}

.test-tips {
  margin-top: 15px;
}

.test-tips ul {
  margin: 10px 0 0 0;
  padding-left: 20px;
}

.test-tips li {
  margin: 5px 0;
}

.pdf-viewer-area {
  flex: 1;
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.no-pdf-placeholder {
  flex: 1;
  background: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  margin-bottom: 20px;
}

.placeholder-content {
  text-align: center;
  color: #999;
}

.placeholder-content i {
  margin-bottom: 20px;
  color: #ddd;
}

.placeholder-content h3 {
  margin-bottom: 10px;
  color: #666;
}

.optimization-details {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.optimization-details h3 {
  margin-bottom: 20px;
  color: #333;
}

.tech-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.tech-card {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #409eff;
}

.tech-card h4 {
  margin-bottom: 8px;
  color: #333;
  font-size: 16px;
}

.tech-card p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .pdf-smooth-test {
    padding: 10px;
  }
  
  .control-group {
    flex-direction: column;
    align-items: stretch;
  }
  
  .control-group label {
    margin-bottom: 8px;
  }
  
  .control-group .el-input {
    width: 100% !important;
    margin-right: 0 !important;
    margin-bottom: 10px;
  }
  
  .tech-cards {
    grid-template-columns: 1fr;
  }
}
</style>
